<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { invoke } from '@tauri-apps/api/core'

// 定义组件的 props 和 emits
interface Props {
  isMinimized?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isMinimized: false
})

// 响应式数据
const pythonStatus = ref<{
  success: boolean
  data?: string
  error?: string
}>({ success: false })

const yimiaoJueStatus = ref<{
  running: boolean
  message: string
}>({ running: false, message: '检查中...' })

const ocrStatus = ref<any>({})
const autoMode = ref(false)
const pythonRunning = ref(false)
const isLoading = ref(false)
const statusMessage = ref('正在初始化...')
const isExpanded = ref(false)

// 计算属性
const statusIcon = computed(() => {
  if (!pythonRunning.value) return '🔴'
  return autoMode.value ? '🟢' : '🟡'
})

const statusText = computed(() => {
  if (!pythonRunning.value) return 'OCR未运行'
  return autoMode.value ? 'OCR自动模式' : 'OCR手动模式'
})

// 检查Python环境
const checkPythonEnvironment = async () => {
  try {
    statusMessage.value = '检查Python环境...'
    const result = await invoke('check_python_environment')
    pythonStatus.value = result as any
    
    if (pythonStatus.value.success) {
      statusMessage.value = 'Python环境正常'
    } else {
      statusMessage.value = 'Python环境异常'
    }
  } catch (error) {
    console.error('检查Python环境失败:', error)
    pythonStatus.value = {
      success: false,
      error: `检查失败: ${error}`
    }
    statusMessage.value = 'Python环境检查失败'
  }
}

// 检查YimiaoJue状态
const checkYimiaoJueStatus = async () => {
  try {
    statusMessage.value = '检查弈秒决状态...'
    const result = await invoke('check_yimiaojue_status')
    yimiaoJueStatus.value = result as any
    
    if (yimiaoJueStatus.value.running) {
      statusMessage.value = '弈秒决就绪'
    } else {
      statusMessage.value = '弈秒决未就绪'
    }
  } catch (error) {
    console.error('检查弈秒决状态失败:', error)
    yimiaoJueStatus.value = {
      running: false,
      message: `检查失败: ${error}`
    }
    statusMessage.value = '弈秒决状态检查失败'
  }
}

// 启动弈秒决
const startYimiaoJue = async () => {
  try {
    isLoading.value = true
    statusMessage.value = '启动弈秒决...'

    const result = await invoke('start_yimiaojue')
    const startResult = result as any

    if (startResult.success) {
      statusMessage.value = '弈秒决启动成功'
      pythonRunning.value = true

      // 等待3秒让Python程序完全启动，然后获取状态
      setTimeout(async () => {
        await getOCRStatus()
      }, 3000)

      // 重新检查状态
      await checkYimiaoJueStatus()
    } else {
      statusMessage.value = '弈秒决启动失败'
      pythonRunning.value = false
      console.error('启动失败:', startResult.error)
    }
  } catch (error) {
    console.error('启动弈秒决失败:', error)
    statusMessage.value = '弈秒决启动失败'
    pythonRunning.value = false
  } finally {
    isLoading.value = false
  }
}

// 获取OCR状态
const getOCRStatus = async () => {
  if (!pythonRunning.value) {
    statusMessage.value = '请先启动弈秒决程序'
    return
  }

  try {
    isLoading.value = true
    statusMessage.value = '获取OCR状态...'

    const result = await invoke('get_ocr_status')
    ocrStatus.value = result as any

    if (ocrStatus.value.success && ocrStatus.value.data) {
      const statusData = JSON.parse(ocrStatus.value.data)
      autoMode.value = statusData.auto_mode
      statusMessage.value = 'OCR状态获取成功'
    } else {
      statusMessage.value = 'OCR状态获取失败'
    }
  } catch (error) {
    console.error('获取OCR状态失败:', error)
    ocrStatus.value = {
      success: false,
      error: `获取失败: ${error}`
    }
    statusMessage.value = 'OCR状态获取失败'
  } finally {
    isLoading.value = false
  }
}

// 切换自动模式
const toggleAutoMode = async () => {
  if (!pythonRunning.value) {
    statusMessage.value = '请先启动弈秒决程序'
    return
  }

  try {
    isLoading.value = true
    statusMessage.value = '切换自动模式...'

    const result = await invoke('toggle_auto_mode')
    const toggleResult = result as any

    if (toggleResult.success && toggleResult.data) {
      const responseData = JSON.parse(toggleResult.data)
      autoMode.value = responseData.auto_mode
      statusMessage.value = `自动模式已${responseData.action === 'started' ? '启动' : '停止'}`
    } else {
      statusMessage.value = '切换自动模式失败'
    }
  } catch (error) {
    console.error('切换自动模式失败:', error)
    statusMessage.value = '切换自动模式失败'
  } finally {
    isLoading.value = false
  }
}

// 手动扫描海克斯
const manualScanHex = async () => {
  if (!pythonRunning.value) {
    statusMessage.value = '请先启动弈秒决程序'
    return
  }

  try {
    isLoading.value = true
    statusMessage.value = '手动扫描海克斯...'

    const result = await invoke('manual_scan_hex')
    const scanResult = result as any

    if (scanResult.success) {
      statusMessage.value = '海克斯扫描完成'
    } else {
      statusMessage.value = '海克斯扫描失败'
    }
  } catch (error) {
    console.error('手动扫描海克斯失败:', error)
    statusMessage.value = '海克斯扫描失败'
  } finally {
    isLoading.value = false
  }
}

// 手动扫描装备
const manualScanEquipment = async () => {
  if (!pythonRunning.value) {
    statusMessage.value = '请先启动弈秒决程序'
    return
  }

  try {
    isLoading.value = true
    statusMessage.value = '手动扫描装备...'

    const result = await invoke('manual_scan_equipment')
    const scanResult = result as any

    if (scanResult.success) {
      statusMessage.value = '装备扫描完成'
    } else {
      statusMessage.value = '装备扫描失败'
    }
  } catch (error) {
    console.error('手动扫描装备失败:', error)
    statusMessage.value = '装备扫描失败'
  } finally {
    isLoading.value = false
  }
}

// 自动初始化OCR
const autoInitializeOCR = async () => {
  console.log('🚀 开始自动初始化OCR...')
  
  // 1. 检查Python环境
  await checkPythonEnvironment()
  
  if (!pythonStatus.value.success) {
    console.error('❌ Python环境检查失败，跳过OCR初始化')
    return
  }
  
  // 2. 检查弈秒决状态
  await checkYimiaoJueStatus()
  
  // 3. 启动弈秒决程序
  await startYimiaoJue()
  
  if (pythonRunning.value) {
    // 4. 等待程序完全启动后，启动自动模式
    setTimeout(async () => {
      console.log('🔄 启动自动模式...')
      if (!autoMode.value) {
        await toggleAutoMode()
      }
    }, 5000) // 等待5秒确保程序完全启动
  }
}

// 组件挂载时自动初始化
onMounted(async () => {
  await autoInitializeOCR()
})

// 切换展开状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}
</script>

<template>
  <div class="ocr-control" :class="{ 'minimized': props.isMinimized }">
    <!-- OCR状态指示器和触发按钮 -->
    <div 
      class="ocr-trigger"
      @click="toggleExpanded"
      :title="statusText"
    >
      <span class="status-icon">{{ statusIcon }}</span>
      <span v-if="!props.isMinimized" class="status-text">{{ statusText }}</span>
      <span class="expand-icon" :class="{ 'expanded': isExpanded }">▼</span>
    </div>

    <!-- 展开的控制面板 -->
    <div 
      v-show="isExpanded" 
      class="ocr-panel"
      :class="{ 'minimized': props.isMinimized }"
    >
      <!-- 状态信息 -->
      <div class="status-info">
        <div class="status-item">
          <span class="label">状态:</span>
          <span class="value">{{ statusMessage }}</span>
        </div>
        <div v-if="isLoading" class="loading-indicator">
          <div class="spinner"></div>
          <span>{{ statusMessage }}</span>
        </div>
      </div>

      <!-- 快速控制按钮 -->
      <div class="quick-controls">
        <button
          @click="toggleAutoMode"
          class="control-btn"
          :class="{ 'active': autoMode, 'disabled': !pythonRunning }"
          :disabled="isLoading || !pythonRunning"
          :title="autoMode ? '停止自动模式' : '启动自动模式'"
        >
          {{ autoMode ? '🛑' : '▶️' }}
        </button>

        <button
          @click="manualScanHex"
          class="control-btn"
          :disabled="isLoading || !pythonRunning"
          title="扫描海克斯"
        >
          🔮
        </button>

        <button
          @click="manualScanEquipment"
          class="control-btn"
          :disabled="isLoading || !pythonRunning"
          title="扫描装备"
        >
          ⚔️
        </button>

        <button
          @click="startYimiaoJue"
          class="control-btn restart-btn"
          :disabled="isLoading"
          title="重启OCR程序"
        >
          🔄
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.ocr-control {
  position: relative;
  z-index: 100;
}

.ocr-trigger {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 40px;
  justify-content: center;
}

.ocr-trigger:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.status-icon {
  font-size: 1rem;
  line-height: 1;
}

.status-text {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
}

.expand-icon {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.6);
  transition: transform 0.2s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.ocr-panel {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 1rem;
  min-width: 280px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.ocr-panel.minimized {
  min-width: 200px;
}

.status-info {
  margin-bottom: 1rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
}

.status-item .label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.status-item .value {
  color: rgba(255, 255, 255, 0.9);
  text-align: right;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.5rem;
}

.spinner {
  width: 12px;
  height: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.quick-controls {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
}

.control-btn {
  padding: 0.5rem;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 36px;
}

.control-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.control-btn.active {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.control-btn.restart-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.control-btn.disabled {
  opacity: 0.3;
}

/* 收起状态下的样式调整 */
.ocr-control.minimized .ocr-trigger {
  padding: 0.4rem;
  min-width: 32px;
}

.ocr-control.minimized .status-text {
  display: none;
}

.ocr-control.minimized .expand-icon {
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ocr-panel {
    min-width: 240px;
  }

  .quick-controls {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
