<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { invoke } from '@tauri-apps/api/core'
import HeroListView from './components/views/HeroListView.vue'
import HeroDetailView from './components/views/HeroDetailView.vue'
import ItemListView from './components/views/ItemListView.vue'
import ItemDetailView from './components/views/ItemDetailView.vue'
import HexListView from './components/views/HexListView.vue'
import OCRControl from './components/OCRControl.vue'

// === 应用状态管理 ===
const isMinimized = ref(false)
const currentTab = ref('英雄')
const currentView = ref('list') // 'list' | 'detail'
const selectedHeroName = ref('')
const selectedHexName = ref('')
const selectedItemName = ref('')

// === 导航配置 ===
const navigationTabs = [
  { name: '阵容' },
  { name: '英雄' },
  { name: '装备' },
  { name: '海克斯' }
]

// === 应用状态管理（只保留框架相关状态） ===

// === 窗口控制功能（保留Tauri API调用） ===
const toggleMinimize = async () => {
  try {
    console.log('切换窗口大小...')
    await invoke('toggle_window_size')
    // 获取实际的窗口状态
    const actualMinimized = await invoke<boolean>('is_window_minimized')
    isMinimized.value = actualMinimized
    console.log('窗口状态已切换:', actualMinimized ? '收起' : '展开')
  } catch (error) {
    console.error('窗口切换失败:', error)
    // 如果Tauri调用失败，仍然切换UI状态
    isMinimized.value = !isMinimized.value
  }
}

const startDrag = async (event: MouseEvent) => {
  // 阻止事件冒泡，避免触发其他事件
  event.preventDefault()
  event.stopPropagation()
  
  try {
    console.log('开始拖拽窗口...')
    await invoke('start_drag')
  } catch (error) {
    console.error('窗口拖拽失败:', error)
  }
}

// === 组件挂载时的初始化 ===
onMounted(async () => {
  // 保存初始窗口尺寸
  try {
    await invoke('save_original_size')
    console.log('已保存初始窗口尺寸')
  } catch (error) {
    console.error('保存初始窗口尺寸失败:', error)
  }
})

const closeApp = async () => {
  try {
    console.log('关闭应用...')
    await invoke('close_app')
  } catch (error) {
    console.error('关闭应用失败:', error)
    // 备用关闭方法
    if ((window as any).__TAURI__) {
      (window as any).__TAURI__.process.exit(0)
    } else {
      window.close()
    }
  }
}

// === 导航功能 ===
const switchTab = (tabName: string) => {
  currentTab.value = tabName
  currentView.value = 'list' // 切换标签时重置为列表视图
  selectedHeroName.value = ''
  selectedHexName.value = ''
  selectedItemName.value = ''
  console.log(`切换到${tabName}页面`)
}

// === 英雄详情页面功能 ===
const handleHeroSelected = (heroName: string) => {
  selectedHeroName.value = heroName
  currentView.value = 'detail'
  console.log(`切换到英雄详情页面: ${heroName}`)
}

const handleBackToList = () => {
  currentView.value = 'list'
  selectedHeroName.value = ''
  selectedHexName.value = ''
  selectedItemName.value = ''
  console.log('返回列表页面')
}

const handleBackToHeroList = () => {
  handleBackToList()
}

const handleItemSelected = (itemName: string) => {
  selectedItemName.value = itemName
  currentView.value = 'detail'
  currentTab.value = '装备' // 切换到装备页面
  console.log(`切换到装备详情页面: ${itemName}`)
}

const handleItemClick = (itemName: string) => {
  selectedItemName.value = itemName
  currentView.value = 'detail'
  console.log(`切换到装备详情页面: ${itemName}`)
}

const handleHeroClick = (heroName: string) => {
  selectedHeroName.value = heroName
  currentView.value = 'detail'
  currentTab.value = '英雄' // 切换到英雄页面
  console.log(`切换到英雄详情页面: ${heroName}`)
}

// 海克斯页面不再支持详情功能


</script>

<template>
  <!-- 全新TFT助手 - 完整应用重构 -->
  <div id="app" class="tft-app">
    <!-- 渐变背景层 -->
    <div class="glassmorphism-background">
      
      <!-- 主应用窗口 -->
      <div 
        class="main-app-window"
        :class="{ 'minimized': isMinimized }"
      >
        
        <!-- 控制栏 - 左侧收放，中间拖拽，右侧关闭 -->
        <div class="control-bar">
          <!-- 左侧控制区域 -->
          <div class="control-left">
            <!-- 返回按钮 -->
            <button 
              v-if="currentView === 'detail' && (selectedHeroName || selectedItemName)"
              @click="handleBackToList"
              class="back-button-header"
              title="返回列表"
            >
              ←
            </button>
            
            <!-- 收放按钮 -->
            <button 
              @click="toggleMinimize"
              class="toggle-button"
              :title="isMinimized ? '展开窗口' : '收起窗口'"
            >
              <div class="hamburger-icon" :class="{ 'minimized': isMinimized }">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </button>
          </div>
          
          <!-- 中央拖拽区域 -->
          <div 
            class="drag-area"
            @mousedown="startDrag"
            title="拖拽窗口"
          >
            <span class="app-title">弈秒决</span>
          </div>
          
          <!-- 右侧控制区域 -->
          <div class="control-right">
            <!-- OCR控制组件 -->
            <OCRControl :is-minimized="isMinimized" />

            <!-- 关闭按钮 -->
            <button
              @click="closeApp"
              class="close-button"
              title="关闭应用"
            >
              ×
            </button>
          </div>
        </div>

        <!-- 主内容区域（收起时隐藏） -->
        <div 
          v-show="!isMinimized" 
          class="main-content"
        >
          
          <!-- 导航栏 - 收起时也显示 -->
          <div class="navigation-bar" :class="{ 'minimized': isMinimized }">
            <button 
              v-for="tab in navigationTabs" 
              :key="tab.name"
              class="nav-button"
              :class="{ 'active': tab.name === currentTab, 'minimized': isMinimized }"
              @click="switchTab(tab.name)"
            >
              <span class="nav-text" v-show="!isMinimized">{{ tab.name }}</span>
            </button>
          </div>

          <!-- 内容区域 -->
          <div class="content-area">
            
            <!-- 英雄页面内容 -->
            <div v-if="currentTab === '英雄'" class="hero-page-container">
              <!-- 英雄列表页面 -->
              <HeroListView
                v-if="currentView === 'list'"
                @hero-selected="handleHeroSelected"
              />

              <!-- 英雄详情页面 -->
              <div v-else-if="currentView === 'detail'" class="hero-detail-container">
                <!-- 英雄详情内容 -->
                <HeroDetailView
                  :hero-name="selectedHeroName"
                  @item-selected="handleItemSelected"
                  @back-to-list="handleBackToList"
                />
              </div>
            </div>

            <!-- 装备页面内容 -->
            <div v-else-if="currentTab === '装备'" class="item-page-container">
              <!-- 装备列表页面 -->
              <ItemListView
                v-if="currentView === 'list'"
                @item-click="handleItemClick"
              />

              <!-- 装备详情页面 -->
              <div v-else-if="currentView === 'detail'" class="item-detail-container">
                <!-- 装备详情内容 -->
                <ItemDetailView
                  :item-name="selectedItemName"
                  @back="handleBackToList"
                  @hero-click="handleHeroClick"
                />
              </div>
            </div>

            <!-- 海克斯页面内容 -->
            <HexListView v-else-if="currentTab === '海克斯'" />

            <!-- 其他页面占位符 -->
            <div v-else class="placeholder-page">
              <div class="placeholder-card">
                <h3>{{ currentTab }}</h3>
                <p>{{ currentTab }}页面开发中...</p>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
/* === 全局样式重置 === */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow: hidden;
  background-color: transparent;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
}

/* === 应用容器 === */
#app {
  height: 100vh;
  width: 100vw;
}

.tft-app {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: stretch;
  justify-content: stretch;
  padding: 0;
  margin: 0;
}

/* === 渐变背景 === */
.glassmorphism-background {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: stretch;
  justify-content: stretch;
  position: relative;
  /* 云顶之弈风格：柔和的紫蓝色渐变 */
  background: linear-gradient(45deg, 
    rgb(88, 86, 134) 0%,     /* 中等紫色 */
    rgb(75, 85, 145) 25%,    /* 紫蓝色 */
    rgb(65, 95, 160) 50%,    /* 蓝紫色 */
    rgb(55, 105, 180) 75%,   /* 蓝色 */
    rgb(45, 115, 200) 100%   /* 亮蓝色 */
  );
  /* 添加柔和的光晕效果 */
  background-attachment: fixed;
}

/* 移除了条纹动画，使用静态渐变 */

/* === 主应用窗口 === */
.main-app-window {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border-radius: 0;  /* 去除圆角，完全填充 */
  border: none;      /* 去除边框 */
  box-shadow: none;  /* 去除阴影 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.main-app-window.minimized {
  height: 48px;
}

@keyframes windowFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* === 控制栏 === */
.control-bar {
  height: 48px;
  display: flex;
  align-items: center;
  padding: 0 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  user-select: none;
  background: rgba(255, 255, 255, 0.02);
  transition: all 0.2s ease;
}

/* 左侧控制区域 */
.control-left {
  width: 96px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
}

/* 中央拖拽区域 */
.drag-area {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: move;
  transition: background-color 0.2s ease;
  border-radius: 4px;
  margin: 0 0.5rem;
}

.drag-area:hover {
  background: rgba(255, 255, 255, 0.05);
}

.app-title {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 500;
  pointer-events: none;
}

/* 右侧控制区域 */
.control-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.5rem;
  min-width: 120px;
}

.control-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

/* 返回按钮 */
.back-button-header {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.15);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 900;
  line-height: 1;
  padding: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.back-button-header:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: scale(1.05);
}

/* 收放按钮 */
.toggle-button {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.toggle-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

/* 汉堡包菜单图标 */
.hamburger-icon {
  width: 18px;
  height: 14px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 0.3s ease;
}

.hamburger-icon span {
  width: 100%;
  height: 2px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1px;
  transition: all 0.3s ease;
}

/* 收起状态的动画 */
.hamburger-icon.minimized span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-icon.minimized span:nth-child(2) {
  opacity: 0;
}

.hamburger-icon.minimized span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.toggle-button:hover .hamburger-icon span {
  background: white;
}

/* 关闭按钮 */
.close-button {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #ff6b6b;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
}

.close-button:hover {
  background: rgba(255, 107, 107, 0.2);
  color: #ff5252;
  transform: scale(1.05);
}

/* === 主内容区域 === */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* === 导航栏 === */
.navigation-bar {
  height: 44px;
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
  transition: all 0.3s ease;
}

.navigation-bar.minimized {
  height: 44px; /* 保持高度，只隐藏文字 */
}

.nav-button {
  flex: 1;
  border: none;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 0.5rem;
  font-size: 14px;
  font-weight: 500;
}

.nav-button.minimized {
  padding: 0.75rem 0.5rem;
}

.nav-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.nav-button.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.nav-button.active {
  font-weight: 600;
}

.nav-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, rgb(88, 86, 134), rgb(65, 95, 160));
  border-radius: 2px 2px 0 0;
}

/* === 内容区域 === */
.content-area {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* === 英雄页面样式已移至HeroListView.vue === */

/* === 占位符页面 === */
.placeholder-page {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.placeholder-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  color: white;
  max-width: 300px;
}



.placeholder-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.placeholder-card p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
}

/* === 页面容器 === */
.hero-page-container,
.item-page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.hero-detail-container,
.item-detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}



/* === 响应式设计 === */
@media (max-width: 768px) {
  .hero-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 0.5rem;
  }
  
  .cost-filter-button {
    font-size: 11px;
    padding: 0.4rem 0.8rem;
  }
}
</style>
